# Project Structure

## Turborepo Monorepo Organization

The project follows a modern monorepo structure using Turborepo for efficient builds, caching, and task orchestration across multiple applications and shared packages.

```
search-keywords-scraper/
├── apps/                           # Application packages
│   ├── web/                        # Next.js frontend application
│   ├── api/                        # NestJS backend API
│   └── worker/                     # Node.js worker processes
├── packages/                       # Shared packages
│   ├── types/                      # Shared TypeScript types
│   ├── config/                     # Shared configuration files
│   ├── database/                   # Drizzle ORM schema and utilities
│   └── utils/                      # Shared utility functions
├── docs/                           # Project documentation
├── .github/                        # GitHub workflows and templates
├── tools/                          # Development tools and scripts
├── turbo.json                      # Turborepo configuration
├── package.json                    # Root package.json
├── pnpm-workspace.yaml            # PNPM workspace configuration
├── .env.example                    # Environment variables template
└── README.md                       # Project overview
```

## Application Structure

### Frontend Application (apps/web)

Next.js 14 application with App Router and TypeScript.

```
apps/web/
├── src/
│   ├── app/                        # Next.js App Router
│   │   ├── (auth)/                 # Route groups for authentication
│   │   │   ├── signin/
│   │   │   │   └── page.tsx
│   │   │   └── signup/
│   │   │       └── page.tsx
│   │   ├── (dashboard)/            # Protected dashboard routes
│   │   │   ├── upload/
│   │   │   │   └── page.tsx
│   │   │   ├── keywords/
│   │   │   │   ├── page.tsx
│   │   │   │   └── [id]/
│   │   │   │       └── page.tsx
│   │   │   └── results/
│   │   │       ├── page.tsx
│   │   │       └── [keywordId]/
│   │   │           └── page.tsx
│   │   ├── api/                    # API routes (if needed)
│   │   │   └── auth/
│   │   │       └── [...nextauth]/
│   │   │           └── route.ts
│   │   ├── globals.css             # Global styles
│   │   ├── layout.tsx              # Root layout
│   │   ├── loading.tsx             # Global loading UI
│   │   ├── error.tsx               # Global error UI
│   │   ├── not-found.tsx           # 404 page
│   │   └── page.tsx                # Home page
│   ├── components/                 # React components
│   │   ├── ui/                     # shadcn/ui components
│   │   │   ├── button.tsx
│   │   │   ├── input.tsx
│   │   │   ├── table.tsx
│   │   │   ├── dialog.tsx
│   │   │   ├── toast.tsx
│   │   │   └── ...
│   │   ├── forms/                  # Form components
│   │   │   ├── upload-form.tsx
│   │   │   ├── search-form.tsx
│   │   │   └── filter-form.tsx
│   │   ├── data-display/           # Data visualization components
│   │   │   ├── keyword-table.tsx
│   │   │   ├── batch-card.tsx
│   │   │   ├── progress-indicator.tsx
│   │   │   └── search-results-viewer.tsx
│   │   ├── layout/                 # Layout components
│   │   │   ├── header.tsx
│   │   │   ├── sidebar.tsx
│   │   │   ├── footer.tsx
│   │   │   └── navigation.tsx
│   │   └── providers/              # Context providers
│   │       ├── auth-provider.tsx
│   │       ├── query-provider.tsx
│   │       └── theme-provider.tsx
│   ├── hooks/                      # Custom React hooks
│   │   ├── use-keywords.ts
│   │   ├── use-upload.ts
│   │   ├── use-search.ts
│   │   └── use-debounce.ts
│   ├── lib/                        # Utility libraries
│   │   ├── api.ts                  # API client configuration
│   │   ├── auth.ts                 # NextAuth.js configuration
│   │   ├── utils.ts                # Utility functions
│   │   └── validations.ts          # Zod validation schemas
│   ├── styles/                     # Styling files
│   │   ├── globals.css             # Global CSS
│   │   └── components.scss         # Component-specific SASS
│   └── types/                      # TypeScript type definitions
│       ├── api.ts                  # API response types
│       ├── auth.ts                 # Authentication types
│       └── components.ts           # Component prop types
├── public/                         # Static assets
│   ├── icons/
│   ├── images/
│   └── favicon.ico
├── .env.local                      # Local environment variables
├── .env.example                    # Environment template
├── components.json                 # shadcn/ui configuration
├── next.config.js                  # Next.js configuration
├── package.json                    # Dependencies and scripts
├── tailwind.config.js              # Tailwind CSS configuration
├── tsconfig.json                   # TypeScript configuration
└── README.md                       # Application documentation
```

### Backend API (apps/api)

NestJS application with modular architecture and TypeScript.

```
apps/api/
├── src/
│   ├── app.module.ts               # Root application module
│   ├── main.ts                     # Application entry point
│   ├── auth/                       # Authentication module
│   │   ├── auth.module.ts
│   │   ├── auth.middleware.ts      # JWT verification middleware
│   │   ├── auth.guard.ts           # Route guards
│   │   ├── auth.decorator.ts       # Custom decorators
│   │   └── strategies/             # Passport strategies
│   │       └── jwt.strategy.ts
│   ├── keywords/                   # Keywords management module
│   │   ├── keywords.module.ts
│   │   ├── keywords.controller.ts
│   │   ├── keywords.service.ts
│   │   ├── dto/                    # Data Transfer Objects
│   │   │   ├── create-keyword.dto.ts
│   │   │   ├── upload-keywords.dto.ts
│   │   │   ├── pagination.dto.ts
│   │   │   └── keyword-response.dto.ts
│   │   └── entities/               # Database entities
│   │       └── keyword.entity.ts
│   ├── upload/                     # File upload module
│   │   ├── upload.module.ts
│   │   ├── upload.controller.ts
│   │   ├── upload.service.ts
│   │   ├── csv-parser.service.ts
│   │   └── dto/
│   │       └── upload-file.dto.ts
│   ├── queue/                      # Job queue module
│   │   ├── queue.module.ts
│   │   ├── queue.service.ts
│   │   ├── queue.processor.ts
│   │   └── dto/
│   │       └── job-data.dto.ts
│   ├── database/                   # Database module
│   │   ├── database.module.ts
│   │   ├── database.service.ts
│   │   └── migrations/             # Database migrations
│   │       ├── 001_initial_schema.sql
│   │       ├── 002_add_indexes.sql
│   │       └── ...
│   ├── common/                     # Shared utilities
│   │   ├── decorators/             # Custom decorators
│   │   │   ├── current-user.decorator.ts
│   │   │   └── api-paginated-response.decorator.ts
│   │   ├── filters/                # Exception filters
│   │   │   ├── global-exception.filter.ts
│   │   │   └── validation-exception.filter.ts
│   │   ├── guards/                 # Guards
│   │   │   ├── jwt-auth.guard.ts
│   │   │   └── rate-limit.guard.ts
│   │   ├── interceptors/           # Interceptors
│   │   │   ├── logging.interceptor.ts
│   │   │   └── transform.interceptor.ts
│   │   ├── pipes/                  # Validation pipes
│   │   │   └── validation.pipe.ts
│   │   └── types/                  # Common types
│   │       ├── paginated-response.type.ts
│   │       └── api-response.type.ts
│   ├── config/                     # Configuration
│   │   ├── configuration.ts        # App configuration
│   │   ├── database.config.ts      # Database configuration
│   │   ├── redis.config.ts         # Redis configuration
│   │   └── validation.schema.ts    # Environment validation
│   └── utils/                      # Utility functions
│       ├── logger.ts               # Winston logger setup
│       ├── crypto.ts               # Cryptographic utilities
│       └── file-validator.ts       # File validation utilities
├── test/                           # Test files
│   ├── app.e2e-spec.ts            # End-to-end tests
│   ├── keywords.e2e-spec.ts
│   ├── upload.e2e-spec.ts
│   ├── fixtures/                   # Test fixtures
│   │   ├── test-keywords.csv
│   │   └── test-data.json
│   └── utils/                      # Test utilities
│       ├── test-setup.ts
│       └── mock-factories.ts
├── .env                            # Environment variables
├── .env.example                    # Environment template
├── package.json                    # Dependencies and scripts
├── tsconfig.json                   # TypeScript configuration
├── tsconfig.build.json             # Build TypeScript configuration
├── nest-cli.json                   # NestJS CLI configuration
├── jest.config.js                  # Jest testing configuration
└── README.md                       # API documentation
```

### Worker Application (apps/worker)

Node.js TypeScript application for processing scraping jobs.

```
apps/worker/
├── src/
│   ├── index.ts                    # Worker entry point
│   ├── worker.ts                   # Main worker class
│   ├── config/                     # Configuration
│   │   ├── index.ts                # Configuration loader
│   │   ├── scraper.config.ts       # Scraper configuration
│   │   ├── database.config.ts      # Database configuration
│   │   └── redis.config.ts         # Redis configuration
│   ├── core/                       # Core scraping functionality
│   │   ├── browser/                # Browser management
│   │   │   ├── browser-manager.ts
│   │   │   ├── stealth-config.ts
│   │   │   ├── context-pool.ts
│   │   │   └── types.ts
│   │   ├── proxy/                  # Proxy management
│   │   │   ├── proxy-manager.ts
│   │   │   ├── proxy-pool.ts
│   │   │   ├── providers/
│   │   │   │   ├── proxy-provider.interface.ts
│   │   │   │   └── upstash-provider.ts
│   │   │   └── types.ts
│   │   ├── scraper/                # Scraping logic
│   │   │   ├── google-scraper.ts   # Enhanced from POC
│   │   │   ├── bing-scraper.ts     # Optional Bing support
│   │   │   ├── search-result-parser.ts
│   │   │   ├── ad-detector.ts
│   │   │   ├── link-counter.ts
│   │   │   └── types.ts
│   │   └── captcha/                # Captcha handling
│   │       ├── captcha-detector.ts
│   │       ├── captcha-solver.ts   # Optional integration
│   │       └── types.ts
│   ├── processors/                 # Job processors
│   │   ├── keyword-processor.ts
│   │   ├── batch-processor.ts
│   │   └── cleanup-processor.ts
│   ├── services/                   # Business services
│   │   ├── scraper.service.ts
│   │   ├── database.service.ts
│   │   ├── notification.service.ts
│   │   └── metrics.service.ts
│   ├── utils/                      # Utility functions
│   │   ├── delay.ts                # Rate limiting utilities
│   │   ├── retry.ts                # Retry logic
│   │   ├── logger.ts               # Winston logger
│   │   ├── file-writer.ts          # File operations
│   │   └── html-parser.ts          # HTML parsing utilities
│   └── types/                      # TypeScript types
│       ├── job.types.ts
│       ├── scraper.types.ts
│       └── config.types.ts
├── test/                           # Test files
│   ├── unit/                       # Unit tests
│   │   ├── scraper.test.ts
│   │   ├── parser.test.ts
│   │   └── proxy-manager.test.ts
│   ├── integration/                # Integration tests
│   │   ├── scraping-workflow.test.ts
│   │   └── database-operations.test.ts
│   ├── fixtures/                   # Test fixtures
│   │   ├── sample-google-page.html
│   │   └── mock-responses.json
│   └── setup.ts                    # Test setup
├── logs/                           # Log files (gitignored)
├── results/                        # Scraping results (gitignored)
├── .env                            # Environment variables
├── .env.example                    # Environment template
├── package.json                    # Dependencies and scripts
├── tsconfig.json                   # TypeScript configuration
├── jest.config.js                  # Jest configuration
└── README.md                       # Worker documentation
```

## Shared Packages

### Types Package (packages/types)

Shared TypeScript type definitions across all applications.

```
packages/types/
├── src/
│   ├── api/                        # API-related types
│   │   ├── requests.ts             # Request DTOs
│   │   ├── responses.ts            # Response DTOs
│   │   └── errors.ts               # Error types
│   ├── database/                   # Database entity types
│   │   ├── user.ts
│   │   ├── keyword.ts
│   │   ├── batch.ts
│   │   └── search-result.ts
│   ├── scraper/                    # Scraping-related types
│   │   ├── search-options.ts
│   │   ├── search-results.ts
│   │   ├── proxy-config.ts
│   │   └── browser-config.ts
│   ├── queue/                      # Job queue types
│   │   ├── job-data.ts
│   │   ├── job-options.ts
│   │   └── job-results.ts
│   ├── auth/                       # Authentication types
│   │   ├── user.ts
│   │   ├── session.ts
│   │   └── tokens.ts
│   ├── common/                     # Common utility types
│   │   ├── pagination.ts
│   │   ├── filters.ts
│   │   ├── status.ts
│   │   └── metadata.ts
│   └── index.ts                    # Package exports
├── package.json
├── tsconfig.json
└── README.md
```

### Database Package (packages/database)

Drizzle ORM schema definitions and database utilities.

```
packages/database/
├── src/
│   ├── schema/                     # Database schema definitions
│   │   ├── users.ts
│   │   ├── keyword-batches.ts
│   │   ├── keywords.ts
│   │   ├── search-results.ts
│   │   ├── relations.ts
│   │   └── index.ts
│   ├── migrations/                 # Database migrations
│   │   ├── 0001_initial.sql
│   │   ├── 0002_add_indexes.sql
│   │   └── meta/
│   │       ├── _journal.json
│   │       └── snapshot.json
│   ├── queries/                    # Reusable queries
│   │   ├── keywords.ts
│   │   ├── batches.ts
│   │   └── users.ts
│   ├── types/                      # Database types
│   │   ├── schema.ts
│   │   └── queries.ts
│   ├── utils/                      # Database utilities
│   │   ├── connection.ts
│   │   ├── seed.ts
│   │   └── backup.ts
│   └── index.ts                    # Package exports
├── drizzle.config.ts               # Drizzle configuration
├── package.json
├── tsconfig.json
└── README.md
```

### Config Package (packages/config)

Shared configuration files and environment handling.

```
packages/config/
├── src/
│   ├── database.ts                 # Database configuration
│   ├── redis.ts                    # Redis configuration
│   ├── auth.ts                     # Authentication configuration
│   ├── scraper.ts                  # Scraper configuration
│   ├── logging.ts                  # Logging configuration
│   ├── validation.ts               # Environment validation schemas
│   ├── constants.ts                # Application constants
│   └── index.ts                    # Package exports
├── package.json
├── tsconfig.json
└── README.md
```

### Utils Package (packages/utils)

Shared utility functions and helpers.

```
packages/utils/
├── src/
│   ├── crypto/                     # Cryptographic utilities
│   │   ├── hash.ts
│   │   ├── encrypt.ts
│   │   └── random.ts
│   ├── validation/                 # Validation utilities
│   │   ├── schemas.ts              # Zod schemas
│   │   ├── sanitizers.ts
│   │   └── validators.ts
│   ├── formatting/                 # Data formatting
│   │   ├── date.ts
│   │   ├── number.ts
│   │   └── string.ts
│   ├── file/                       # File utilities
│   │   ├── csv-parser.ts
│   │   ├── file-validator.ts
│   │   └── mime-detector.ts
│   ├── network/                    # Network utilities
│   │   ├── http-client.ts
│   │   ├── retry.ts
│   │   └── rate-limiter.ts
│   ├── async/                      # Async utilities
│   │   ├── delay.ts
│   │   ├── timeout.ts
│   │   └── queue.ts
│   └── index.ts                    # Package exports
├── package.json
├── tsconfig.json
└── README.md
```

## Configuration Files

### Root Configuration

```
# package.json (root)
{
  "name": "search-keywords-scraper",
  "private": true,
  "workspaces": [
    "apps/*",
    "packages/*"
  ],
  "scripts": {
    "build": "turbo run build",
    "dev": "turbo run dev --parallel",
    "lint": "turbo run lint",
    "test": "turbo run test",
    "type-check": "turbo run type-check",
    "clean": "turbo run clean",
    "db:migrate": "turbo run db:migrate",
    "db:seed": "turbo run db:seed",
    "format": "prettier --write \"**/*.{ts,tsx,js,jsx,json,md}\""
  },
  "devDependencies": {
    "@turbo/gen": "^1.10.12",
    "turbo": "^1.10.12",
    "prettier": "^3.0.3",
    "eslint": "^8.48.0",
    "typescript": "^5.0.0"
  },
  "packageManager": "pnpm@8.6.10"
}
```

```yaml
# pnpm-workspace.yaml
packages:
  - "apps/*"
  - "packages/*"
```

### Turborepo Configuration

```json
# turbo.json
{
  "$schema": "https://turbo.build/schema.json",
  "globalDependencies": ["**/.env.*local"],
  "pipeline": {
    "build": {
      "dependsOn": ["^build"],
      "outputs": [".next/**", "!.next/cache/**", "dist/**"]
    },
    "dev": {
      "cache": false,
      "persistent": true
    },
    "lint": {
      "dependsOn": ["^build"]
    },
    "test": {
      "dependsOn": ["^build"],
      "outputs": ["coverage/**"]
    },
    "type-check": {
      "dependsOn": ["^build"]
    },
    "clean": {
      "cache": false
    },
    "db:migrate": {
      "cache": false
    },
    "db:seed": {
      "cache": false
    }
  }
}
```

## Development Workflow

### File Naming Conventions

1. **React Components**: PascalCase (e.g., `KeywordTable.tsx`)
2. **Hooks**: camelCase with "use" prefix (e.g., `useKeywords.ts`)
3. **Utilities**: camelCase (e.g., `formatDate.ts`)
4. **Types**: camelCase with descriptive names (e.g., `keyword.types.ts`)
5. **Constants**: SCREAMING_SNAKE_CASE (e.g., `API_ENDPOINTS.ts`)
6. **Pages/Routes**: kebab-case (e.g., `keyword-details.tsx`)
7. **Directories**: kebab-case or camelCase based on context

### Import Organization

```typescript
// 1. External libraries
import React from 'react';
import { NextPage } from 'next';
import { useQuery } from '@tanstack/react-query';

// 2. Internal packages (from packages/)
import { KeywordBatch } from '@repo/types';
import { db } from '@repo/database';

// 3. Relative imports (same app)
import { useKeywords } from '../../hooks/use-keywords';
import { KeywordTable } from '../components/keyword-table';
import { Button } from '../components/ui/button';
import { formatDate } from '../utils/date';

// 4. Type-only imports (separated)
import type { ComponentProps } from 'react';
import type { ApiResponse } from '@repo/types';
```

### Environment Variables

Each application maintains its own environment configuration while sharing common variables through the config package.

```bash
# .env.example (root)
# Database
DATABASE_URL="postgresql://user:pass@localhost:5432/keywords_db"

# Redis
REDIS_URL="redis://localhost:6379"

# Authentication
NEXTAUTH_SECRET="your-secret-here"
NEXTAUTH_URL="http://localhost:3000"

# Supabase
SUPABASE_URL="https://your-project.supabase.co"
SUPABASE_ANON_KEY="your-anon-key"

# External Services
PROXY_PROVIDER_API_KEY="your-proxy-api-key"

# Application
NODE_ENV="development"
LOG_LEVEL="info"
```

This project structure provides a scalable foundation that promotes code reuse, maintains clear separation of concerns, and enables efficient development workflows across the entire application ecosystem.