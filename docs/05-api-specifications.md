# API Specifications

## Overview

The Search Keywords Scraper API is built with NestJS and follows RESTful conventions. It provides endpoints for user authentication, keyword batch management, file uploads, and search result retrieval. All endpoints use JSON for data exchange and JWT tokens for authentication.

## Base Configuration

- **Base URL**: `https://api.search-scraper.com` (production) / `http://localhost:3001` (development)
- **API Version**: v1
- **Content-Type**: `application/json`
- **Authentication**: Bearer JWT tokens (NextAuth.js)

## Authentication

### Overview
Authentication is handled by NextAuth.js on the frontend, which generates JWT tokens. The API validates these tokens using a custom middleware.

```typescript
// Authentication Header Format
Authorization: Bearer <jwt_token>
```

### JWT Token Structure
```json
{
  "sub": "user-uuid",
  "email": "<EMAIL>",
  "iat": 1634567890,
  "exp": 1634654290,
  "iss": "search-keywords-scraper"
}
```

## Error Response Format

All API errors follow a consistent format:

```json
{
  "statusCode": 400,
  "timestamp": "2024-01-15T10:30:00.000Z",
  "path": "/api/keywords/upload",
  "method": "POST",
  "message": "File validation failed: CSV format required",
  "details": {
    "field": "file",
    "code": "INVALID_FILE_FORMAT"
  }
}
```

### HTTP Status Codes
- `200` - Success
- `201` - Created
- `400` - Bad Request (validation error)
- `401` - Unauthorized (invalid/missing token)
- `403` - Forbidden (insufficient permissions)
- `404` - Not Found
- `422` - Unprocessable Entity (business logic error)
- `429` - Too Many Requests (rate limiting)
- `500` - Internal Server Error

## API Endpoints

### 1. Authentication

**Note**: Authentication is handled by Supabase Auth. The API validates JWT tokens issued by Supabase and extracts user context for authorization.

#### Get Current User Session
```http
GET /api/auth/me
Authorization: Bearer <token>
```

**Response:**
```json
{
  "id": "user-uuid",
  "email": "<EMAIL>",
  "name": "John Doe",
  "createdAt": "2024-01-01T00:00:00.000Z"
}
```

### 2. Frontend CSV Processing

**Note**: CSV upload and processing happens entirely on the frontend using Papa Parse. The frontend validates, parses, and converts CSV files to keyword arrays before sending to the API.

#### CSV Processing Workflow:
1. User selects CSV file in the frontend
2. Frontend validates file size (5MB max) and format
3. Papa Parse library parses CSV content
4. Frontend extracts keywords from first column
5. Zod validation applied to each keyword
6. Duplicate keywords removed
7. Preview shown to user with validation results
8. User confirms and keywords array sent to API

### 3. Keyword Batches

#### Create Keyword Batch
```http
POST /api/keywords/batch
Authorization: Bearer <token>
Content-Type: application/json

{
  "keywords": ["best laptops 2024", "gaming chairs", "wireless headphones"],
  "batchName": "Q4 keyword research batch", // optional
  "description": "Technology product keywords" // optional
}
```

**Request Validation:**
- Keywords array must contain 1-100 items
- Each keyword must be 1-500 characters
- Keywords are automatically trimmed and deduplicated
- Invalid characters are rejected

**Response:**
```json
{
  "id": "batch-uuid",
  "batchName": "Q4 keyword research batch",
  "description": "Technology product keywords",
  "totalKeywords": 3,
  "processedKeywords": 0,
  "successfulKeywords": 0,
  "failedKeywords": 0,
  "status": "pending",
  "processingStartedAt": null,
  "processingCompletedAt": null,
  "createdAt": "2024-01-15T10:30:00.000Z",
  "updatedAt": "2024-01-15T10:30:00.000Z"
}
```

#### Get User's Keyword Batches
```http
GET /api/batches?page=1&limit=20&status=completed&search=keyword
Authorization: Bearer <token>
```

**Query Parameters:**
- `page` (optional): Page number (default: 1)
- `limit` (optional): Items per page (default: 20, max: 100)
- `status` (optional): Filter by status (`pending`, `processing`, `completed`, `failed`)
- `search` (optional): Search in filenames and descriptions

**Response:**
```json
{
  "data": [
    {
      "id": "batch-uuid",
      "filename": "keywords.csv",
      "description": "Q4 keyword research batch",
      "totalKeywords": 25,
      "processedKeywords": 25,
      "successfulKeywords": 23,
      "failedKeywords": 2,
      "status": "completed",
      "processingStartedAt": "2024-01-15T10:30:00.000Z",
      "processingCompletedAt": "2024-01-15T10:45:00.000Z",
      "createdAt": "2024-01-15T10:30:00.000Z",
      "updatedAt": "2024-01-15T10:45:00.000Z"
    }
  ],
  "pagination": {
    "page": 1,
    "limit": 20,
    "total": 45,
    "totalPages": 3
  }
}
```

#### Get Batch Details
```http
GET /api/batches/{batchId}
Authorization: Bearer <token>
```

**Response:**
```json
{
  "id": "batch-uuid",
  "filename": "keywords.csv",
  "description": "Q4 keyword research batch",
  "totalKeywords": 25,
  "processedKeywords": 25,
  "successfulKeywords": 23,
  "failedKeywords": 2,
  "status": "completed",
  "processingStartedAt": "2024-01-15T10:30:00.000Z",
  "processingCompletedAt": "2024-01-15T10:45:00.000Z",
  "createdAt": "2024-01-15T10:30:00.000Z",
  "updatedAt": "2024-01-15T10:45:00.000Z",
  "keywords": [
    {
      "id": "keyword-uuid",
      "keyword": "best laptops 2024",
      "status": "completed",
      "retryCount": 0,
      "processingTimeMs": 3450,
      "createdAt": "2024-01-15T10:30:00.000Z",
      "updatedAt": "2024-01-15T10:32:00.000Z"
    }
  ]
}
```

#### Delete Batch
```http
DELETE /api/batches/{batchId}
Authorization: Bearer <token>
```

**Response:**
```json
{
  "message": "Batch deleted successfully",
  "deletedCount": {
    "batch": 1,
    "keywords": 25,
    "searchResults": 23
  }
}
```

### 3. Keywords Management

#### Get User's Keywords
```http
GET /api/keywords?page=1&limit=20&batchId=batch-uuid&status=completed&search=laptop
Authorization: Bearer <token>
```

**Query Parameters:**
- `page` (optional): Page number (default: 1)
- `limit` (optional): Items per page (default: 20, max: 100)
- `batchId` (optional): Filter by batch ID
- `status` (optional): Filter by status
- `search` (optional): Search in keyword text

**Response:**
```json
{
  "data": [
    {
      "id": "keyword-uuid",
      "keyword": "best laptops 2024",
      "status": "completed",
      "retryCount": 0,
      "processingTimeMs": 3450,
      "batchId": "batch-uuid",
      "batchName": "Q4 keyword research batch",
      "createdAt": "2024-01-15T10:30:00.000Z",
      "updatedAt": "2024-01-15T10:32:00.000Z",
      "searchResult": {
        "totalAds": 8,
        "totalLinks": 156,
        "searchEngine": "google",
        "scrapedAt": "2024-01-15T10:32:00.000Z"
      }
    }
  ],
  "pagination": {
    "page": 1,
    "limit": 20,
    "total": 150,
    "totalPages": 8
  }
}
```

#### Get Keyword Details
```http
GET /api/keywords/{keywordId}
Authorization: Bearer <token>
```

**Response:**
```json
{
  "id": "keyword-uuid",
  "keyword": "best laptops 2024",
  "status": "completed",
  "retryCount": 0,
  "processingTimeMs": 3450,
  "processingStartedAt": "2024-01-15T10:30:30.000Z",
  "processingCompletedAt": "2024-01-15T10:32:15.000Z",
  "errorMessage": null,
  "createdAt": "2024-01-15T10:30:00.000Z",
  "updatedAt": "2024-01-15T10:32:15.000Z",
  "batch": {
    "id": "batch-uuid",
    "batchName": "Q4 keyword research batch",
    "description": "Technology product keywords"
  },
  "searchResult": {
    "id": "result-uuid",
    "totalAds": 8,
    "totalLinks": 156,
    "searchEngine": "google",
    "pageTitle": "best laptops 2024 - Google Search",
    "searchUrl": "https://www.google.com/search?q=best+laptops+2024",
    "processingTimeMs": 3450,
    "proxyUsed": "proxy-server-01",
    "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
    "metadata": {
      "adsBreakdown": {
        "textAds": 5,
        "shoppingAds": 3,
        "videoAds": 0
      },
      "linkCategories": {
        "organic": 10,
        "ads": 8,
        "relatedSearches": 8,
        "navigation": 130
      }
    },
    "scrapedAt": "2024-01-15T10:32:15.000Z"
  }
}
```

#### Retry Failed Keyword
```http
POST /api/keywords/{keywordId}/retry
Authorization: Bearer <token>
```

**Response:**
```json
{
  "message": "Keyword queued for retry",
  "keywordId": "keyword-uuid",
  "jobId": "job-uuid",
  "retryCount": 1
}
```

### 4. Search Results

#### Get Search Result with HTML Cache
```http
GET /api/search-results/{resultId}
Authorization: Bearer <token>
```

**Response:**
```json
{
  "id": "result-uuid",
  "keywordId": "keyword-uuid",
  "keyword": "best laptops 2024",
  "totalAds": 8,
  "totalLinks": 156,
  "searchEngine": "google",
  "pageTitle": "best laptops 2024 - Google Search",
  "searchUrl": "https://www.google.com/search?q=best+laptops+2024",
  "htmlCache": "<!DOCTYPE html><html>...</html>",
  "processingTimeMs": 3450,
  "proxyUsed": "proxy-server-01",
  "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
  "metadata": {
    "adsBreakdown": {
      "textAds": 5,
      "shoppingAds": 3,
      "videoAds": 0
    },
    "linkCategories": {
      "organic": 10,
      "ads": 8,
      "relatedSearches": 8,
      "navigation": 130
    },
    "scrapingMetrics": {
      "browserStartupTime": 1200,
      "pageLoadTime": 2250,
      "parsingTime": 150
    }
  },
  "scrapedAt": "2024-01-15T10:32:15.000Z"
}
```

#### Download HTML Cache
```http
GET /api/search-results/{resultId}/html
Authorization: Bearer <token>
```

**Response Headers:**
```
Content-Type: text/html
Content-Disposition: attachment; filename="best_laptops_2024_cache.html"
```

**Response Body:** Raw HTML content

### 5. Search & Analytics

#### Global Search Across All Keywords
```http
GET /api/search?q=laptop&type=keyword&sortBy=totalAds&sortOrder=desc&page=1&limit=20
Authorization: Bearer <token>
```

**Query Parameters:**
- `q` (required): Search query
- `type` (optional): Search type (`keyword`, `result`, `batch`) - default: `keyword`
- `sortBy` (optional): Sort field (`createdAt`, `totalAds`, `totalLinks`, `processingTime`)
- `sortOrder` (optional): Sort direction (`asc`, `desc`) - default: `desc`
- `page` (optional): Page number - default: 1
- `limit` (optional): Items per page - default: 20

**Response:**
```json
{
  "data": [
    {
      "type": "keyword",
      "id": "keyword-uuid",
      "keyword": "gaming laptops 2024",
      "status": "completed",
      "batchName": "Q4 keyword research batch",
      "totalAds": 12,
      "totalLinks": 189,
      "searchEngine": "google",
      "scrapedAt": "2024-01-15T10:32:15.000Z",
      "relevanceScore": 0.95
    }
  ],
  "pagination": {
    "page": 1,
    "limit": 20,
    "total": 8,
    "totalPages": 1
  },
  "aggregations": {
    "totalKeywords": 8,
    "avgAdsPerKeyword": 9.2,
    "avgLinksPerKeyword": 167.5,
    "searchEngineBreakdown": {
      "google": 8,
      "bing": 0
    }
  }
}
```

#### Analytics Dashboard Data
```http
GET /api/analytics/dashboard?period=30d
Authorization: Bearer <token>
```

**Query Parameters:**
- `period` (optional): Time period (`7d`, `30d`, `90d`, `1y`) - default: `30d`

**Response:**
```json
{
  "summary": {
    "totalBatches": 12,
    "totalKeywords": 347,
    "totalProcessed": 334,
    "totalFailed": 13,
    "successRate": 96.25,
    "avgProcessingTime": 3200,
    "totalAdsFound": 2847,
    "totalLinksFound": 52891
  },
  "trends": {
    "batchesOverTime": [
      { "date": "2024-01-01", "count": 2 },
      { "date": "2024-01-02", "count": 1 },
      { "date": "2024-01-03", "count": 3 }
    ],
    "processingTimeOverTime": [
      { "date": "2024-01-01", "avgTime": 3100 },
      { "date": "2024-01-02", "avgTime": 3350 },
      { "date": "2024-01-03", "avgTime": 2950 }
    ]
  },
  "topKeywords": [
    {
      "keyword": "best smartphones 2024",
      "totalAds": 15,
      "totalLinks": 234,
      "processingTime": 2800
    }
  ],
  "errorAnalysis": {
    "timeouts": 8,
    "captchas": 3,
    "networkErrors": 2,
    "other": 0
  }
}
```

## Request/Response Examples

### CSV Upload with Curl
```bash
curl -X POST \
  -H "Authorization: Bearer <token>" \
  -F "file=@keywords.csv" \
  -F "description=SEO keyword research batch" \
  http://localhost:3001/api/keywords/upload
```

### Polling for Batch Progress
```javascript
const pollBatchProgress = async (batchId) => {
  const response = await fetch(`/api/batches/${batchId}`, {
    headers: {
      'Authorization': `Bearer ${token}`
    }
  });
  
  const batch = await response.json();
  
  if (batch.status === 'processing') {
    const progress = (batch.processedKeywords / batch.totalKeywords) * 100;
    console.log(`Progress: ${progress.toFixed(1)}%`);
    
    // Continue polling
    setTimeout(() => pollBatchProgress(batchId), 5000);
  }
};
```

## Rate Limiting

### Limits
- **File Upload**: 5 requests per minute per user
- **API Requests**: 100 requests per minute per user
- **Search Operations**: 20 requests per minute per user

### Rate Limit Headers
```http
X-RateLimit-Limit: 100
X-RateLimit-Remaining: 95
X-RateLimit-Reset: 1642234567
```

### Rate Limit Exceeded Response
```json
{
  "statusCode": 429,
  "timestamp": "2024-01-15T10:30:00.000Z",
  "path": "/api/keywords/upload",
  "method": "POST",
  "message": "Rate limit exceeded. Please try again later.",
  "details": {
    "limit": 5,
    "remaining": 0,
    "resetTime": "2024-01-15T10:31:00.000Z"
  }
}
```

## WebSocket Events (Future Phase)

### Connection
```javascript
const socket = io('ws://localhost:3001', {
  auth: {
    token: supabaseToken
  }
});
```

### Event Types
```javascript
// Keyword processing started
socket.on('keyword:processing', (data) => {
  console.log(`Processing: ${data.keyword}`);
});

// Keyword processing completed
socket.on('keyword:completed', (data) => {
  console.log(`Completed: ${data.keyword} - ${data.totalAds} ads found`);
});

// Keyword processing failed
socket.on('keyword:failed', (data) => {
  console.log(`Failed: ${data.keyword} - ${data.error}`);
});

// Batch status update
socket.on('batch:progress', (data) => {
  console.log(`Batch ${data.batchId}: ${data.completed}/${data.total} completed`);
});
```

## OpenAPI Specification

The complete OpenAPI 3.0 specification is available at:
- **Development**: `http://localhost:3001/api/docs`
- **Production**: `https://api.search-scraper.com/api/docs`

### Swagger UI Features
- Interactive API documentation
- Request/response examples
- Authentication testing
- Schema validation
- Export to Postman/Insomnia

This API specification provides a comprehensive guide for integrating with the Search Keywords Scraper platform, enabling efficient keyword processing and result management through a robust, well-documented interface.