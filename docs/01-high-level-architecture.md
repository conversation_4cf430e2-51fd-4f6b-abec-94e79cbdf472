# High Level Architecture

## Problem Statement

We need to build a web application that extracts large amounts of data from Google/Bing search results while working around anti-scraping limitations. The system must handle:

1. **User Authentication**: Secure user registration and login
2. **File Processing**: CSV upload with 1-100 keywords
3. **Distributed Scraping**: Scalable keyword processing with anti-detection
4. **Data Storage**: Persistent storage of search results and metrics
5. **Real-time Updates**: Progressive result display as keywords are processed
6. **Search & Discovery**: User-friendly interface for viewing and searching results

## High Level System Overview

```mermaid
graph TB
    subgraph "Client Layer"
        U[Users] --> WEB[Next.js Web App]
    end
    
    subgraph "Authentication"
        WEB --> AUTH[NextAuth.js + Supabase Auth]
    end
    
    subgraph "API Layer"
        WEB --> API[NestJS API Server]
        AUTH --> API
    end
    
    subgraph "Queue System"
        API --> QUEUE[Bull Queue + Redis]
        QUEUE --> WORKERS[Worker Processes]
    end
    
    subgraph "Scraping Infrastructure"
        WORKERS --> SCRAPER[Enhanced Google Scraper]
        SCRAPER --> PROXIES[Proxy Pool]
        SCRAPER --> BROWSER[Playwright + Stealth]
    end
    
    subgraph "Data Layer"
        API --> DB[(PostgreSQL Database)]
        WORKERS --> DB
        AUTH --> DB
    end
    
    subgraph "External Services"
        SCRAPER --> GOOGLE[Google Search]
        SCRAPER --> BING[Bing Search]
    end
```

## Core Problems & Solutions

### 1. Anti-Scraping Limitations

**Problem**: Google/Bing implement sophisticated anti-bot measures including:
- Rate limiting
- IP blocking
- CAPTCHA challenges
- Browser fingerprinting
- Behavioral analysis

**Solution**: Multi-layered anti-detection strategy
```mermaid
graph LR
    A[Keyword Request] --> B[Proxy Pool Manager]
    B --> C[Browser Context Pool]
    C --> D[Stealth Configuration]
    D --> E[Human Behavior Simulation]
    E --> F[Rate Limiting]
    F --> G[Search Execution]
    G --> H{Success?}
    H -->|No| I[Circuit Breaker]
    H -->|Yes| J[Parse Results]
    I --> K[Retry with Different Proxy]
    K --> B
```

### 2. Scalable Processing

**Problem**: Processing 1-100 keywords efficiently while maintaining anti-detection measures

**Solution**: Queue-based worker architecture with distributed processing
```mermaid
sequenceDiagram
    participant U as User
    participant API as NestJS API
    participant Q as Bull Queue
    participant W1 as Worker 1
    participant W2 as Worker 2
    participant DB as Database
    
    U->>API: Upload CSV (100 keywords)
    API->>DB: Store batch & keywords
    API->>Q: Enqueue 100 jobs
    
    par Worker 1 Processing
        Q->>W1: Dequeue job 1
        W1->>W1: Scrape with proxy A
        W1->>DB: Store results
    and Worker 2 Processing
        Q->>W2: Dequeue job 2
        W2->>W2: Scrape with proxy B
        W2->>DB: Store results
    end
    
    API->>U: Real-time progress updates
```

### 3. Data Extraction & Storage

**Problem**: Extract specific metrics (ads count, links count, HTML cache) and store efficiently

**Solution**: Structured parsing with caching strategy
```mermaid
graph TD
    A[HTML Page] --> B[SearchResultParser]
    B --> C[Count Ads]
    B --> D[Count Links]
    B --> E[Cache HTML]
    C --> F[Store Metrics]
    D --> F
    E --> F
    F --> G[(PostgreSQL)]
    
    subgraph "Ad Detection Strategy"
        C --> C1[Selector-based Detection]
        C --> C2[Text Content Analysis]
        C --> C3[DOM Structure Analysis]
    end
```

### 4. User Experience & Real-time Updates

**Problem**: Provide immediate feedback and progressive result display

**Solution**: Event-driven architecture with optimistic updates
```mermaid
graph LR
    A[User Upload] --> B[Immediate Batch Creation]
    B --> C[Queue Jobs]
    C --> D[Polling for Updates]
    D --> E[Progress Display]
    
    subgraph "Real-time Flow"
        F[Worker Completion] --> G[Database Update]
        G --> H[Status Change]
        H --> I[UI Refresh]
    end
```

## System Components

### 1. Frontend Layer
- **Next.js 14**: Modern React framework with App Router
- **NextAuth.js**: Authentication with Supabase provider
- **shadcn/ui**: Professional UI components
- **TanStack Query**: Server state management with caching

### 2. API Layer
- **NestJS**: Enterprise-grade Node.js framework
- **Drizzle ORM**: Type-safe database operations
- **Bull Queue**: Robust job queue with Redis backend
- **JWT Middleware**: Token-based authentication

### 3. Worker Layer
- **Node.js Workers**: Scalable processing units
- **Enhanced GoogleScraper**: Proven scraping architecture
- **Playwright**: Headless browser automation
- **Proxy Management**: Rotating proxy pools for anti-detection

### 4. Data Layer
- **PostgreSQL**: Relational database for structured data
- **Redis**: In-memory store for queue management and caching
- **Supabase**: Managed PostgreSQL with authentication

## Security & Compliance

```mermaid
graph TB
    subgraph "Security Layers"
        A[Frontend Authentication] --> B[JWT Token Validation]
        B --> C[API Authorization]
        C --> D[Database Row-Level Security]
    end
    
    subgraph "Anti-Detection"
        E[Proxy Rotation] --> F[User-Agent Rotation]
        F --> G[Browser Fingerprint Randomization]
        G --> H[Human Behavior Simulation]
    end
    
    subgraph "Data Protection"
        I[Input Validation] --> J[SQL Injection Prevention]
        J --> K[XSS Protection]
        K --> L[CSRF Protection]
    end
```

## Scalability Considerations

### Horizontal Scaling
- **API Servers**: Multiple NestJS instances behind load balancer
- **Worker Processes**: Auto-scaling based on queue depth
- **Database**: Read replicas for query optimization
- **Redis**: Cluster mode for high availability

### Performance Optimization
- **Connection Pooling**: Efficient database connections
- **Browser Context Reuse**: Minimize browser startup overhead
- **Intelligent Proxy Selection**: Route requests through fastest proxies
- **Result Caching**: Cache parsed results to avoid re-processing

## Deployment Architecture

```mermaid
graph TB
    subgraph "Production Environment"
        LB[Load Balancer] --> API1[NestJS API 1]
        LB --> API2[NestJS API 2]
        
        API1 --> DB[(Supabase PostgreSQL)]
        API2 --> DB
        
        API1 --> REDIS[(Upstash Redis)]
        API2 --> REDIS
        
        REDIS --> W1[Worker 1]
        REDIS --> W2[Worker 2]
        REDIS --> W3[Worker N...]
        
        CDN[Vercel CDN] --> NEXT[Next.js App]
        NEXT --> LB
    end
    
    subgraph "External Services"
        DB --> SUPA[Supabase Auth]
        REDIS --> UPSTASH[Upstash Cloud]
    end
```

This high-level architecture provides a robust foundation for building a scalable search keyword scraper that can handle anti-bot measures while delivering excellent user experience and maintaining data integrity.