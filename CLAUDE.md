# CLAUDE.md

This file provides guidance to Claude <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is a **Search Keywords Scraper** web application that extracts data from Google/Bing search results pages. The system processes CSV files containing keywords (1-100 per batch), scrapes search engine results pages, and extracts metrics including total ads count, total links count, and HTML cache of pages.

**Key Technical Challenge**: Working around search engine anti-scraping measures including rate limiting, IP blocking, CAPTCHA challenges, and browser fingerprinting through sophisticated proxy rotation, stealth browser configurations, and intelligent retry mechanisms.

## Architecture

**Turborepo Monorepo Structure**:
- `apps/web/` - Next.js 15+ frontend with App Router and TypeScript
- `apps/api/` - NestJS backend API with Drizzle ORM
- `apps/worker/` - Node.js TypeScript worker processes for scraping
- `packages/types/` - Shared TypeScript type definitions  
- `packages/database/` - Drizzle ORM schema and utilities
- `packages/config/` - Shared configuration files
- `packages/utils/` - Shared utility functions

**Technology Stack**:
- **Frontend**: Next.js 15+, TypeScript, Tailwind CSS, shadcn/ui, TanStack Query, NextAuth.js
- **Backend**: NestJS, TypeScript, Drizzle ORM, Bull Queue, Express.js
- **Workers**: Node.js, TypeScript, Playwright, Bull Queue consumer
- **Database**: PostgreSQL (Supabase hosted)
- **Queue**: Redis (Upstash hosted)
- **Authentication**: Supabase Auth with JWT tokens

## Core Workflow

1. **CSV Processing**: Frontend parses CSV files using Papa Parse, validates keywords (1-500 chars, max 100 per batch), and sends keyword arrays to API
2. **Batch Creation**: API creates batch record and individual keyword records, enqueues scraping jobs in Redis
3. **Worker Processing**: Worker processes consume jobs, use enhanced scraper with proxy rotation and stealth browser automation
4. **Data Extraction**: Parse search results to count ads, count links, cache full HTML content
5. **Real-time Updates**: Frontend polls API for batch progress updates every 5 seconds

## Development Commands

**Root level commands (run from project root)**:
```bash
# Install dependencies
pnpm install

# Start all development servers
pnpm dev

# Build all applications
pnpm build

# Run tests across all packages
pnpm test

# Type check all packages
pnpm type-check

# Lint all packages
pnpm lint

# Format code
pnpm format
```

**Database operations**:
```bash
# Run database migrations
pnpm db:migrate

# Seed database with test data
pnpm db:seed
```

**Individual app development**:
```bash
# Frontend only (from apps/web/)
pnpm dev

# API only (from apps/api/)
pnpm dev

# Worker only (from apps/worker/)
pnpm dev
```

## Key Implementation Details

### CSV Processing Strategy
- **Frontend-only processing**: CSV validation, parsing, and keyword extraction handled entirely in Next.js app using Papa Parse
- **No file upload to backend**: Keywords array sent to API after frontend validation
- **Validation rules**: 1-100 keywords per batch, 1-500 characters per keyword, automatic deduplication

### Anti-Scraping Architecture
- **Proxy Pool Management**: Rotating proxy servers with health checking
- **Stealth Browser Configuration**: Playwright with anti-detection plugins
- **Human Behavior Simulation**: Random delays, mouse movements, scroll patterns
- **Circuit Breaker Pattern**: Automatic retry with exponential backoff and different proxies
- **Rate Limiting**: Intelligent request spacing to avoid detection

### Database Schema (Drizzle ORM)
**Core entities**:
- `users` - User accounts (Supabase Auth integration)
- `keyword_batches` - Groups of uploaded keywords with processing status
- `keywords` - Individual keywords with retry logic and status tracking  
- `search_results` - Scraped data including ads count, links count, HTML cache

**Key patterns**:
- Row-level security (RLS) for data isolation
- Cascade deletes for data consistency
- Optimized indexes for common query patterns
- JSONB metadata for flexible scraping data storage

### State Management Patterns
- **Server State**: TanStack Query with hierarchical cache keys, 5-second polling for progress
- **Client State**: Zustand for UI state, React Hook Form for forms
- **Authentication**: NextAuth.js with Supabase provider, JWT middleware on API

## Important Constraints

### Security Requirements
- **Row-level security**: All database operations filtered by authenticated user
- **Input validation**: Server-side validation with custom DTOs and Zod schemas
- **Rate limiting**: API endpoints protected against abuse
- **CORS configuration**: Strict origin control for production

### Performance Considerations
- **Browser context pooling**: Reuse browser instances to reduce startup overhead
- **Connection pooling**: Database connections managed efficiently
- **Queue partitioning**: Separate high/low priority job queues
- **Caching strategy**: Redis caching for frequently accessed data

### Business Rules
- **Maximum 100 keywords per batch** (validated on frontend and backend)
- **Maximum 3 retry attempts** per keyword with exponential backoff
- **30-second timeout** per scraping operation
- **User data isolation** enforced at database level

## Error Handling Strategy

**Retry Logic**:
- Network errors, timeouts, rate limits → Retryable with different proxy
- CAPTCHA challenges → Retryable with different proxy/user-agent
- Invalid keywords, permission errors → Not retryable
- Circuit breaker prevents cascading failures

**Monitoring**:
- Structured logging with Winston
- Error tracking with Sentry integration
- Prometheus metrics for worker performance
- Health check endpoints for deployment monitoring

## Development Notes

### Code Style
- **TypeScript strict mode** enabled across all packages
- **Biome** for formatting and linting (not ESLint/Prettier)
- **Consistent naming**: camelCase for variables, PascalCase for types, kebab-case for files
- **No comments in code** unless specifically requested

### Testing Strategy
- **Unit tests**: Jest with comprehensive service layer testing
- **Integration tests**: Full API testing with test database
- **E2E tests**: Playwright for critical user flows

### Deployment
- **Frontend**: Vercel with automatic deployments from main branch
- **Backend/Workers**: DigitalOcean App Platform with auto-scaling
- **Database**: Supabase production with automated backups
- **Queue**: Upstash Redis with persistence enabled

## Common Tasks

### Adding New Features
1. Update shared types in `packages/types/` first
2. Add database schema changes in `packages/database/src/schema/`
3. Run migrations with `pnpm db:migrate` 
4. Implement API endpoints in `apps/api/src/`
5. Add frontend components in `apps/web/src/`
6. Update worker logic in `apps/worker/src/` if needed

### Database Changes
1. Modify schema files in `packages/database/src/schema/`
2. Generate migration: `pnpm db:generate`
3. Apply migration: `pnpm db:migrate`
4. Update TypeScript types automatically generated by Drizzle

### Scraper Enhancements
- Core scraping logic in `apps/worker/src/core/scraper/`
- Proxy management in `apps/worker/src/core/proxy/`
- Browser management in `apps/worker/src/core/browser/`
- Anti-detection measures in stealth configuration files

This project implements a sophisticated scraping system that prioritizes reliability, scalability, and legal compliance while handling the technical challenges of modern search engine anti-bot measures.