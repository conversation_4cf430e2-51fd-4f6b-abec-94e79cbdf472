# Search Keywords Scraper

A sophisticated web application that extracts large amounts of data from Google and Bing search results pages while working around anti-scraping limitations. Built with modern technologies and designed for scalability, reliability, and user experience.

## Key Features

- **Intelligent Web Scraping**: Advanced anti-detection measures with proxy rotation and stealth browser configurations
- **CSV Batch Processing**: Upload and process 1-100 keywords per batch with real-time progress tracking
- **Distributed Architecture**: Scalable queue-based processing with automatic retry mechanisms
- **Real-time Updates**: Live progress monitoring and instant result availability
- **Secure Authentication**: User isolation with JWT-based authentication and row-level security
- **Comprehensive Analytics**: Extract ad counts, link counts, and HTML cache for each keyword

## Architecture Overview

This is a **Turborepo monorepo** built with modern technologies:

- **Frontend**: Next.js 15+ with App Router, TypeScript, Tailwind CSS, shadcn/ui
- **Backend API**: NestJS with Drizzle ORM, Bull Queue, JWT authentication
- **Workers**: Node.js TypeScript workers with Playwright browser automation
- **Database**: PostgreSQL (Supabase) with Redis queue management
- **Infrastructure**: Docker-ready with cloud deployment configurations

```mermaid
graph TB
    subgraph "Client Layer"
        U[Users] --> WEB[Next.js Web App]
    end
    
    subgraph "API Layer"
        WEB --> API[NestJS API Server]
    end
    
    subgraph "Processing Layer"
        API --> QUEUE[Bull Queue + Redis]
        QUEUE --> WORKERS[Worker Processes]
    end
    
    subgraph "Data Layer"
        API --> DB[(PostgreSQL)]
        WORKERS --> DB
    end
    
    subgraph "External Services"
        WORKERS --> GOOGLE[Google Search]
        WORKERS --> BING[Bing Search]
    end
```

## Project Structure

```
search-keywords-scraper/
 apps/
   web/          # Next.js frontend application
   api/          # NestJS backend API
   worker/       # Node.js worker processes
 packages/
   types/        # Shared TypeScript types
   config/       # Shared configuration
   database/     # Drizzle ORM schema
   utils/        # Shared utilities
 docs/             # Comprehensive documentation
 tools/            # Development tools and scripts
```

## Quick Start

### Prerequisites

- Node.js 18+
- pnpm 8+
- PostgreSQL 15+
- Redis 7+

### Development Setup

```bash
# Install dependencies
pnpm install

# Set up environment variables
cp .env.example .env.local

# Run database migrations
pnpm db:migrate

# Start all development servers
pnpm dev
```

### Available Scripts

```bash
# Development
pnpm dev              # Start all apps in development mode
pnpm build            # Build all applications
pnpm test             # Run test suites
pnpm type-check       # TypeScript validation
pnpm lint             # Code linting
pnpm format           # Code formatting

# Database
pnpm db:migrate       # Run database migrations
pnpm db:seed          # Seed with test data
pnpm db:studio        # Open database studio

# Individual Apps
cd apps/web && pnpm dev     # Frontend only
cd apps/api && pnpm dev     # Backend only
cd apps/worker && pnpm dev  # Worker only
```

## Documentation

Our comprehensive documentation is organized for easy navigation:

### Architecture & Design
- **[High-Level Architecture](docs/01-high-level-architecture.md)** - System overview, components, and design decisions
- **[Detailed Architecture](docs/02-detailed-architecture.md)** - Technology stack deep dive and component interactions
- **[Project Structure](docs/04-project-structure.md)** - Monorepo organization and file structure

### Development Guidelines  
- **[Coding Standards](docs/03-coding-standards.md)** - TypeScript standards, naming conventions, and best practices
- **[Testing Strategy](docs/08-testing-strategy.md)** - Comprehensive testing approach with priority-based coverage

### Technical Specifications
- **[API Specifications](docs/05-api-specifications.md)** - REST API endpoints, request/response schemas
- **[Database Schema](docs/07-database-schema.md)** - Data models, relationships, and migration strategy

### Operations & Deployment
- **[Deployment Strategy](docs/06-deployment-strategy.md)** - Production deployment, scaling, and monitoring

## 🗺️ Feature Roadmap

Our development roadmap is organized by priority and tracked through [GitHub Issues](https://github.com/tuandinh0801/search-keywords-scraper/issues) and [Project Milestones](https://github.com/tuandinh0801/search-keywords-scraper/milestones).

### 🔴 Priority 1: Core Foundation (Critical)
**Milestone: [v1.0 - Core Foundation](https://github.com/tuandinh0801/search-keywords-scraper/milestone/1)**

Essential features that define the application's core value proposition.

#### 🔐 User Authentication & Security
- [ ] Supabase Auth integration with JWT tokens ([#1](https://github.com/tuandinh0801/search-keywords-scraper/issues/1))
- [ ] Protected API endpoints and route guards
- [ ] Session management and token refresh

#### 📄 CSV Upload & Processing
- [ ] File upload with validation (1-100 keywords) ([#3](https://github.com/tuandinh0801/search-keywords-scraper/issues/3))
- [ ] CSV parsing with error handling
- [ ] Batch creation and keyword management
- [ ] Input sanitization and security

#### 🤖 Web Scraping Engine
- [ ] Playwright browser automation with stealth configuration ([#4](https://github.com/tuandinh0801/search-keywords-scraper/issues/4))
- [ ] Proxy rotation and health management ([#5](https://github.com/tuandinh0801/search-keywords-scraper/issues/5))
- [ ] Anti-detection measures (fingerprinting, behavior simulation)
- [ ] Robust retry logic with exponential backoff ([#7](https://github.com/tuandinh0801/search-keywords-scraper/issues/7))

#### ⚙️ Queue-Based Job Processing
- [ ] Bull Queue with Redis backend ([#6](https://github.com/tuandinh0801/search-keywords-scraper/issues/6))
- [ ] Distributed worker processes
- [ ] Job failure handling and retry mechanisms
- [ ] Progress tracking and status updates

### 🟡 Priority 2: Enhanced User Experience (High)
**Milestone: [v2.0 - Enhanced UX](https://github.com/tuandinh0801/search-keywords-scraper/milestone/2)**

Features that significantly improve usability and provide competitive advantages.

#### 📊 Real-time Progress Monitoring
- [ ] Live batch processing status ([#8](https://github.com/tuandinh0801/search-keywords-scraper/issues/8))
- [ ] Individual keyword progress tracking
- [ ] Error reporting and retry status
- [ ] WebSocket-based real-time updates (upgrade from polling)

#### 🔍 Search Results Analytics
- [ ] Google/Bing ads count extraction ([#9](https://github.com/tuandinh0801/search-keywords-scraper/issues/9))
- [ ] Total links count analysis
- [ ] HTML page caching
- [ ] Advanced metrics (SERP position tracking, competitor analysis)
- [ ] Result comparison and trending

#### 🔎 Advanced Search & Filtering
- [ ] Basic keyword search and filtering ([#10](https://github.com/tuandinh0801/search-keywords-scraper/issues/10))
- [ ] Advanced filtering by status, date range, metrics
- [ ] Batch comparison and analytics
- [ ] Export functionality (CSV, PDF reports)

### 🟢 Priority 3: Platform Enhancement (Medium)
**Milestone: [v3.0 - Platform Enhancement](https://github.com/tuandinh0801/search-keywords-scraper/milestone/3)**

Features that add significant value and differentiation.

#### 📈 Analytics Dashboard
- [ ] Processing success rate metrics ([#11](https://github.com/tuandinh0801/search-keywords-scraper/issues/11))
- [ ] Performance analytics and trends
- [ ] Resource usage monitoring
- [ ] Custom reporting and insights

#### 🚀 Advanced Scraping Features
- [ ] Multi-search engine support (expand beyond Google/Bing)
- [ ] Geographic location targeting
- [ ] Device-specific scraping (mobile vs desktop results)
- [ ] Historical data tracking and comparison

#### 🎨 UI/UX Improvements
- [ ] Dark/light theme toggle ([#12](https://github.com/tuandinh0801/search-keywords-scraper/issues/12))
- [ ] Responsive mobile optimization
- [ ] Keyboard shortcuts and power user features
- [ ] Accessibility compliance (WCAG 2.1)
- [ ] Export functionality (CSV, PDF reports) ([#13](https://github.com/tuandinh0801/search-keywords-scraper/issues/13))

### 🔵 Priority 4: Enterprise & Scaling (Low)
**Milestone: [v4.0 - Enterprise & Scale](https://github.com/tuandinh0801/search-keywords-scraper/milestone/4)**

Features for enterprise use and large-scale deployments.

#### 🏢 Multi-tenancy & Organizations
- [ ] Organization management
- [ ] Team collaboration features
- [ ] Role-based access control
- [ ] Usage quotas and billing integration

#### ⚡ Performance & Scalability
- [ ] Auto-scaling worker processes ([#14](https://github.com/tuandinh0801/search-keywords-scraper/issues/14))
- [ ] Database optimization and indexing
- [ ] CDN integration for static assets
- [ ] Caching layers for improved performance

#### 🔐 Advanced Security
- [ ] API rate limiting and DDoS protection ([#15](https://github.com/tuandinh0801/search-keywords-scraper/issues/15))
- [ ] Audit logging and compliance
- [ ] Data encryption at rest
- [ ] GDPR compliance features

#### 🤖 AI & Machine Learning
- [ ] Intelligent proxy selection
- [ ] Automatic CAPTCHA solving
- [ ] Keyword suggestion engine ([#16](https://github.com/tuandinh0801/search-keywords-scraper/issues/16))
- [ ] Anomaly detection for scraping failures

## Technology Stack

### Frontend
- **Framework**: Next.js 15+ with App Router
- **Language**: TypeScript 5.0+
- **Styling**: Tailwind CSS + SASS
- **UI Components**: shadcn/ui (Radix UI)
- **State Management**: TanStack Query + Zustand
- **Forms**: React Hook Form + Zod validation

### Backend
- **Framework**: NestJS 10.0+
- **Language**: TypeScript 5.0+
- **Database**: PostgreSQL 15+ with Drizzle ORM
- **Queue**: Bull Queue with Redis 7.0+
- **Authentication**: Supabase Auth with JWT
- **API Documentation**: Swagger/OpenAPI

### Workers & Scraping
- **Runtime**: Node.js 18+
- **Browser Automation**: Playwright with stealth plugins
- **Proxy Management**: Custom proxy rotation system
- **Job Processing**: Bull Queue consumer
- **Monitoring**: Winston logging + Prometheus metrics

### Infrastructure
- **Monorepo**: Turborepo for build optimization
- **Package Manager**: pnpm with workspace support
- **Code Quality**: Biome (formatting/linting)
- **Testing**: Jest + Playwright + Testing Library
- **CI/CD**: GitHub Actions
- **Deployment**: Docker + Digital Ocean / Vercel

## >� Testing Strategy

We follow a priority-based testing approach focused on high-value coverage:

- **60% Unit Tests**: Business logic, validation, algorithms
- **30% Integration Tests**: API endpoints, database operations, queue processing
- **10% E2E Tests**: Critical user journeys and workflows

For detailed testing guidelines, see our [Testing Strategy](docs/08-testing-strategy.md).

## 🚀 Development Workflow

### Issue-Driven Development

We follow an **issue-driven development** approach where all features are tracked through GitHub Issues:

1. **Browse Issues**: Check [open issues](https://github.com/tuandinh0801/search-keywords-scraper/issues) for available work
2. **Pick an Issue**: Choose an issue from the current milestone ([v1.0 Core Foundation](https://github.com/tuandinh0801/search-keywords-scraper/milestone/1))
3. **Create Branch**: Create feature branch from `main` using pattern: `feature/issue-{number}-{short-description}`
4. **Development**: Follow our [Coding Standards](docs/03-coding-standards.md) and [Testing Strategy](docs/08-testing-strategy.md)
5. **Pull Request**: Submit PR linking to the issue with `Closes #issue-number`
6. **Review & Merge**: Code review, testing, and merge to `main`

### Branch Naming Convention

```bash
# Feature branches
feature/issue-1-auth-integration
feature/issue-3-csv-upload
feature/issue-4-playwright-scraper

# Bug fix branches  
fix/issue-17-proxy-timeout
fix/issue-23-memory-leak

# Enhancement branches
enhancement/issue-12-dark-theme
```

### Commit Message Format

```bash
# Link commits to issues
feat: implement Supabase auth integration (#1)
fix: resolve proxy timeout issue (#17)
docs: update API documentation (#25)
test: add CSV validation tests (#3)
```

## 🤝 Contributing

### Development Workflow

1. **Pick an Issue**: Choose from [available issues](https://github.com/tuandinh0801/search-keywords-scraper/issues?q=is%3Aissue+is%3Aopen+label%3A%22good+first+issue%22)
2. **Fork and Clone**: Create your own fork of the repository
3. **Branch Strategy**: Create feature branches from `main` following naming conventions above
4. **Development**: Follow our [Coding Standards](docs/03-coding-standards.md)
5. **Testing**: Ensure all tests pass and add tests for new features
6. **Pull Request**: Submit PR with clear description linking to the issue

### Code Quality Requirements

- TypeScript strict mode compliance
- 80%+ test coverage for new features
- All linting and formatting checks must pass
- Documentation updates for public APIs

## Requirements & Compliance

This project fulfills the following technical requirements:

- **Web Framework**: Next.js with TypeScript
- **Database**: PostgreSQL with Drizzle ORM
- **Authentication**: Secure user authentication and session management
- **File Processing**: CSV upload with 1-100 keyword validation
- **Web Scraping**: Creative solutions for anti-scraping limitations
- **Real-time Updates**: Live progress tracking and result display
- **Testing**: Comprehensive test suite with multiple test types
- **Git Workflow**: Regular commits and pull request workflow

## License

This project is licensed under the MIT License. See the [LICENSE](LICENSE) file for details.