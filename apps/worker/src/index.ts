import { createLogger, format, transports } from 'winston';
import { GoogleScraper } from '@search-keywords-scraper/scraper';
import { scraperConfig, workerConfig } from './config';

const logger = createLogger({
  level: workerConfig.logLevel,
  format: format.combine(
    format.timestamp(),
    format.errors({ stack: true }),
    format.json()
  ),
  defaultMeta: { service: 'worker' },
  transports: [
    new transports.File({ filename: 'logs/worker-error.log', level: 'error' }),
    new transports.File({ filename: 'logs/worker-combined.log' }),
    new transports.Console({
      format: format.combine(
        format.colorize(),
        format.simple()
      )
    })
  ],
});

async function testScrapingFunctionality() {
  logger.info('Testing scraping functionality...');
  
  const scraper = new GoogleScraper(scraperConfig);
  
  try {
    // Initialize the scraper
    await scraper.initialize();
    logger.info('Scraper initialized successfully');
    
    // Test with a simple query
    const testQuery = 'typescript programming language';
    logger.info(`Testing search for: "${testQuery}"`);
    
    const result = await scraper.search({
      query: testQuery,
      page: 1,
      resultsPerPage: 10
    });
    
    logger.info('Search completed successfully', {
      query: result.query,
      resultsCount: result.results.length,
      adsCount: result.adsCount,
      totalLinksCount: result.totalLinksCount,
      htmlCacheLength: result.htmlCache.length,
      hasNextPage: result.hasNextPage
    });
    
    // Log a sample of results for verification
    logger.info('Sample results:', {
      sampleResults: result.results.slice(0, 3).map(r => ({
        title: r.title,
        url: r.url,
        type: r.type,
        position: r.position
      }))
    });
    
    // Clean up
    await scraper.close();
    logger.info('Scraper closed successfully');
    
    return result;
    
  } catch (error) {
    logger.error('Failed to test scraping functionality:', error);
    await scraper.close();
    throw error;
  }
}

async function main() {
  logger.info('Worker starting...', { 
    mode: workerConfig.testMode ? 'TEST' : 'PRODUCTION',
    config: {
      headless: scraperConfig.browser.headless,
      requestsPerHour: scraperConfig.scraping.requestsPerHour,
      concurrency: workerConfig.concurrency
    }
  });
  
  if (workerConfig.testMode) {
    // Run in test mode - single scrape test
    await testScrapingFunctionality();
    logger.info('Test mode completed successfully');
  } else {
    // TODO: Production mode - Bull queue consumer will go here
    logger.info('Production mode - Bull queue consumer not yet implemented');
  }
}

main().catch((error) => {
  logger.error('Failed to start worker:', error);
  process.exit(1);
});