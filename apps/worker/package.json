{"name": "@search-keywords-scraper/worker", "version": "0.1.0", "description": "Worker processes for scraping search engine results", "private": true, "main": "dist/index.js", "scripts": {"build": "tsc", "dev": "tsx watch src/index.ts", "start": "node dist/index.js", "lint": "eslint src/**/*.ts --fix", "type-check": "tsc --noEmit", "format": "prettier --write src/**/*.ts", "test": "jest"}, "dependencies": {"@search-keywords-scraper/config": "workspace:*", "@search-keywords-scraper/scraper": "workspace:*", "@search-keywords-scraper/types": "workspace:*", "@search-keywords-scraper/utils": "workspace:*", "bull": "^4.16.3", "patchright": "^1.52.5", "winston": "^3.17.0", "dotenv": "^16.4.7"}, "devDependencies": {"@types/node": "^22.10.7", "@types/jest": "^30.0.0", "typescript": "^5.7.3", "tsx": "^4.20.4", "eslint": "^9.18.0", "prettier": "^3.4.2", "jest": "^30.0.0", "ts-jest": "^29.2.5"}, "jest": {"preset": "ts-jest", "testEnvironment": "node", "roots": ["<rootDir>/src"], "testMatch": ["**/__tests__/**/*.ts", "**/?(*.)+(spec|test).ts"]}, "license": "MIT"}