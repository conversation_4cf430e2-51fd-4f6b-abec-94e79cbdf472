{"compilerOptions": {"module": "nodenext", "moduleResolution": "nodenext", "resolvePackageJsonExports": true, "esModuleInterop": true, "isolatedModules": true, "declaration": true, "removeComments": true, "emitDecoratorMetadata": true, "experimentalDecorators": true, "allowSyntheticDefaultImports": true, "target": "ES2023", "sourceMap": true, "outDir": "./dist", "baseUrl": "./", "incremental": true, "skipLibCheck": true, "strictNullChecks": true, "forceConsistentCasingInFileNames": true, "noImplicitAny": true, "strictBindCallApply": true, "noFallthroughCasesInSwitch": true, "paths": {"@search-keywords-scraper/types": ["../../packages/types/src"], "@search-keywords-scraper/database": ["../../packages/database/src"], "@search-keywords-scraper/config": ["../../packages/config/src"], "@search-keywords-scraper/utils": ["../../packages/utils/src"]}}, "references": [{"path": "../../packages/types"}, {"path": "../../packages/database"}, {"path": "../../packages/config"}, {"path": "../../packages/utils"}]}