'use client'

import { But<PERSON> } from '@/components/ui/Button'

export function DashboardContent() {
  return (
    <main className="max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8">
      <div className="bg-white overflow-hidden shadow rounded-lg">
        <div className="px-4 py-5 sm:p-6">
          <h2 className="text-lg font-medium text-gray-900 mb-4">
            Dashboard
          </h2>
          <p className="text-gray-600 mb-6">
            Upload CSV files containing keywords to start scraping search results.
          </p>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="bg-blue-50 p-6 rounded-lg">
              <h3 className="text-lg font-semibold text-blue-900 mb-2">
                Upload Keywords
              </h3>
              <p className="text-blue-700 mb-4">
                Upload a CSV file with 1-100 keywords to start processing.
              </p>
              <Button variant="primary">
                Upload CSV
              </Button>
            </div>
            
            <div className="bg-green-50 p-6 rounded-lg">
              <h3 className="text-lg font-semibold text-green-900 mb-2">
                View Results
              </h3>
              <p className="text-green-700 mb-4">
                Monitor processing status and view scraped results.
              </p>
              <Button variant="primary">
                View Results
              </Button>
            </div>
            
            <div className="bg-purple-50 p-6 rounded-lg">
              <h3 className="text-lg font-semibold text-purple-900 mb-2">
                Analytics
              </h3>
              <p className="text-purple-700 mb-4">
                View processing metrics and performance analytics.
              </p>
              <Button variant="primary">
                View Analytics
              </Button>
            </div>
          </div>
        </div>
      </div>
    </main>
  )
}