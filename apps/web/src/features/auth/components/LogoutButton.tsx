'use client'

import { useAuth } from '../hooks/useAuth'
import { Button } from '@/components/ui/Button'

export function LogoutButton() {
  const { signOut, isSigningOut } = useAuth()

  const handleLogout = async () => {
    await signOut()
  }

  return (
    <Button 
      variant="danger" 
      size="sm" 
      onClick={handleLogout}
      loading={isSigningOut}
    >
      Logout
    </Button>
  )
}