import { pgTable, uuid, varchar, timestamp, integer, text, jsonb, pgEnum } from 'drizzle-orm/pg-core';
import { relations } from 'drizzle-orm';

// Enums
export const batchStatusEnum = pgEnum('batch_status', ['pending', 'processing', 'completed', 'failed']);
export const keywordStatusEnum = pgEnum('keyword_status', ['pending', 'processing', 'completed', 'failed']);
export const searchEngineEnum = pgEnum('search_engine', ['google', 'bing']);

// Users table
export const users = pgTable('users', {
  id: uuid('id').primaryKey().defaultRandom(),
  email: varchar('email', { length: 255 }).notNull().unique(),
  createdAt: timestamp('created_at').notNull().defaultNow(),
  updatedAt: timestamp('updated_at').notNull().defaultNow(),
});

// Keyword batches table
export const keywordBatches = pgTable('keyword_batches', {
  id: uuid('id').primaryKey().defaultRandom(),
  userId: uuid('user_id').notNull().references(() => users.id, { onDelete: 'cascade' }),
  name: varchar('name', { length: 255 }).notNull(),
  status: batchStatusEnum('status').notNull().default('pending'),
  totalKeywords: integer('total_keywords').notNull().default(0),
  completedKeywords: integer('completed_keywords').notNull().default(0),
  failedKeywords: integer('failed_keywords').notNull().default(0),
  createdAt: timestamp('created_at').notNull().defaultNow(),
  updatedAt: timestamp('updated_at').notNull().defaultNow(),
});

// Keywords table
export const keywords = pgTable('keywords', {
  id: uuid('id').primaryKey().defaultRandom(),
  batchId: uuid('batch_id').notNull().references(() => keywordBatches.id, { onDelete: 'cascade' }),
  keyword: varchar('keyword', { length: 500 }).notNull(),
  status: keywordStatusEnum('status').notNull().default('pending'),
  retryCount: integer('retry_count').notNull().default(0),
  searchEngine: searchEngineEnum('search_engine').notNull().default('google'),
  createdAt: timestamp('created_at').notNull().defaultNow(),
  updatedAt: timestamp('updated_at').notNull().defaultNow(),
});

// Search results table
export const searchResults = pgTable('search_results', {
  id: uuid('id').primaryKey().defaultRandom(),
  keywordId: uuid('keyword_id').notNull().references(() => keywords.id, { onDelete: 'cascade' }),
  totalAds: integer('total_ads').notNull().default(0),
  totalLinks: integer('total_links').notNull().default(0),
  htmlContent: text('html_content'),
  metadata: jsonb('metadata'),
  scrapedAt: timestamp('scraped_at').notNull().defaultNow(),
});

// Relations
export const usersRelations = relations(users, ({ many }) => ({
  keywordBatches: many(keywordBatches),
}));

export const keywordBatchesRelations = relations(keywordBatches, ({ one, many }) => ({
  user: one(users, {
    fields: [keywordBatches.userId],
    references: [users.id],
  }),
  keywords: many(keywords),
}));

export const keywordsRelations = relations(keywords, ({ one, many }) => ({
  batch: one(keywordBatches, {
    fields: [keywords.batchId],
    references: [keywordBatches.id],
  }),
  searchResults: many(searchResults),
}));

export const searchResultsRelations = relations(searchResults, ({ one }) => ({
  keyword: one(keywords, {
    fields: [searchResults.keywordId],
    references: [keywords.id],
  }),
}));

// Type exports
export type User = typeof users.$inferSelect;
export type NewUser = typeof users.$inferInsert;

export type KeywordBatch = typeof keywordBatches.$inferSelect;
export type NewKeywordBatch = typeof keywordBatches.$inferInsert;

export type Keyword = typeof keywords.$inferSelect;
export type NewKeyword = typeof keywords.$inferInsert;

export type SearchResult = typeof searchResults.$inferSelect;
export type NewSearchResult = typeof searchResults.$inferInsert;