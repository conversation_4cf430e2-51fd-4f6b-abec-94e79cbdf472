import { z } from 'zod';

// Environment configuration schema
export const envSchema = z.object({
  NODE_ENV: z.enum(['development', 'test', 'production']).default('development'),
  PORT: z.string().transform(Number).default('3001'),
  DATABASE_URL: z.string().url(),
  REDIS_URL: z.string().url(),
  JWT_SECRET: z.string().min(32),
  NEXTAUTH_SECRET: z.string().min(32),
  NEXTAUTH_URL: z.string().url().optional(),
});

// Worker environment configuration schema
export const workerEnvSchema = z.object({
  NODE_ENV: z.enum(['development', 'test', 'production']).default('development'),
  TEST_MODE: z.string().transform(val => val === 'true').default('false'),
  WORKER_CONCURRENCY: z.string().transform(Number).default('1'),
  LOG_LEVEL: z.string().default('info'),
  
  // Browser configuration
  HEADLESS: z.string().transform(val => val !== 'false').default('true'),
  SLOW_MO: z.string().transform(Number).default('0'),
  BROWSER_ARGS: z.string().optional(),
  
  // Scraping configuration
  REQUESTS_PER_HOUR: z.string().transform(Number).default('100'),
  MAX_RETRIES: z.string().transform(Number).default('3'),
  RETRY_DELAY: z.string().transform(Number).default('1000'),
  MIN_DELAY: z.string().transform(Number).default('2000'),
  MAX_DELAY: z.string().transform(Number).default('5000'),
  
  // Proxy configuration (optional)
  PROXY_HOST: z.string().optional(),
  PROXY_PORT: z.string().transform(Number).optional(),
  PROXY_USERNAME: z.string().optional(),
  PROXY_PASSWORD: z.string().optional(),
});

export type Environment = z.infer<typeof envSchema>;
export type WorkerEnvironment = z.infer<typeof workerEnvSchema>;

// Validation schemas for API requests
export const createBatchSchema = z.object({
  name: z.string().min(1).max(255),
  keywords: z.array(z.string().min(1).max(500)).min(1).max(100),
  searchEngine: z.enum(['google', 'bing']).default('google'),
});

export const updateBatchSchema = z.object({
  name: z.string().min(1).max(255).optional(),
  status: z.enum(['pending', 'processing', 'completed', 'failed']).optional(),
});

// Configuration constants
export const config = {
  maxKeywordsPerBatch: 100,
  maxKeywordLength: 500,
  maxRetryAttempts: 3,
  scrapingTimeout: 30000, // 30 seconds
  queuePollInterval: 5000, // 5 seconds
  database: {
    maxConnections: 10,
    idleTimeout: 30000,
  },
  redis: {
    maxRetriesPerRequest: 3,
    retryDelayOnFailover: 100,
  },
  scraping: {
    userAgents: [
      'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
      'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
      'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
    ],
    delayRange: {
      min: 1000,
      max: 3000,
    },
  },
} as const;

// Helper functions to create configurations
export function createScraperConfig(env: Partial<WorkerEnvironment> = {}): {
  browser: {
    headless: boolean;
    slowMo: number;
    launchOptions: { args: string[] };
  };
  scraping: {
    requestsPerHour: number;
    maxRetries: number;
    retryDelay: number;
    delayBetweenRequests: [number, number];
  };
} {
  const validated = workerEnvSchema.parse(env);
  
  return {
    browser: {
      headless: validated.HEADLESS,
      slowMo: validated.SLOW_MO,
      launchOptions: {
        args: validated.BROWSER_ARGS?.split(',') || []
      }
    },
    scraping: {
      requestsPerHour: validated.REQUESTS_PER_HOUR,
      maxRetries: validated.MAX_RETRIES,
      retryDelay: validated.RETRY_DELAY,
      delayBetweenRequests: [validated.MIN_DELAY, validated.MAX_DELAY]
    }
  };
}

export function createWorkerConfig(env: Partial<WorkerEnvironment> = {}): {
  concurrency: number;
  logLevel: string;
  testMode: boolean;
} {
  const validated = workerEnvSchema.parse(env);
  
  return {
    concurrency: validated.WORKER_CONCURRENCY,
    logLevel: validated.LOG_LEVEL,
    testMode: validated.TEST_MODE
  };
}