import type { Page, Locator } from 'patchright';
import type { SearchResult, SearchResponse, ParserSelectors } from '../../types';
import { logger } from '../../utils/logger';

export class SearchResultParser {
  private readonly selectors: ParserSelectors = {
    resultContainer: [
      '.g',
      '[data-sokoban-container]',
      '.rc',
      '.g .tF2Cxc',
      '.kvH3mc'
    ],
    title: [
      '.LC20lb',
      'h3',
      '.DKV0Md',
      '.LC20lb.DKV0Md',
      'a h3'
    ],
    url: [
      '.yuRUbf > a',
      '.yuRUbf a',
      'a[href]',
      '.r > a',
      '.kCrYT > a'
    ],
    snippet: [
      '.VwiC3b',
      '.aCOpRe',
      '.s',
      '.st',
      '.IsZvec'
    ],
    adIndicator: [
      '[data-text-ad]',
      '.ads-ad',
      '.ad_cclk',
      '.commercial-unit-desktop-top',
      '.pla-unit'
    ],
    nextPageButton: [
      'a[aria-label="Next"]',
      '#pnnext',
      'a[id="pnnext"]',
      '.nBDE1b.G5eFlf'
    ]
  };

  async parse(page: Page, query: string): Promise<SearchResponse> {
    try {
      const results: SearchResult[] = [];
      const timestamp = new Date();

      // Get result containers
      const containers = await this.getElements(page, this.selectors.resultContainer);
      
      logger.info(`Found ${containers.length} result containers`);

      let position = 1;
      for (const container of containers) {
        try {
          const result = await this.parseResultContainer(container, position);
          if (result) {
            results.push(result);
            position++;
          }
        } catch (error) {
          logger.warn(`Failed to parse result container ${position}:`, error);
        }
      }

      // Check for next page
      const hasNextPage = await this.hasNextPage(page);

      // Get total results (if available)
      const totalResults = await this.getTotalResults(page);

      // Get related queries
      const relatedQueries = await this.getRelatedQueries(page);

      // Count total ads on the page
      const adsCount = await this.countAdsOnPage(page);

      // Count total links on the page
      const totalLinksCount = await this.countLinksOnPage(page);

      // Cache HTML content of the page
      const htmlCache = await this.getPageHtmlCache(page);

      return {
        query,
        totalResults: totalResults || undefined,
        results,
        relatedQueries: relatedQueries.length > 0 ? relatedQueries : undefined,
        timestamp,
        page: this.getCurrentPage(page) || 1,
        hasNextPage,
        // Core metrics required by the project
        adsCount,
        totalLinksCount,
        htmlCache
      };

    } catch (error) {
      logger.error('Failed to parse search results:', error);
      throw error;
    }
  }

  private async parseResultContainer(container: Locator, position: number): Promise<SearchResult | null> {
    try {
      // Check if this is an ad
      const isAd = await this.isAdvertisement(container);

      // Extract title
      const title = await this.extractText(container, this.selectors.title);
      if (!title) {
        return null; // Skip if no title found
      }

      // Extract URL
      const url = await this.extractUrl(container, this.selectors.url);
      if (!url) {
        return null; // Skip if no URL found
      }

      // Extract snippet
      const snippet = await this.extractText(container, this.selectors.snippet) || '';

      // Determine result type
      const type = this.determineResultType(container, isAd);

      // Extract metadata
      const metadata = await this.extractMetadata(container, url);

      return {
        title,
        url,
        snippet,
        position,
        type,
        metadata: metadata || undefined
      };

    } catch (error) {
      logger.warn(`Failed to parse result at position ${position}:`, error);
      return null;
    }
  }

  private async extractText(container: Locator, selectors: string[]): Promise<string | null> {
    for (const selector of selectors) {
      try {
        const element = container.locator(selector).first();
        const text = await element.textContent({ timeout: 1000 });
        if (text && text.trim()) {
          return text.trim();
        }
      } catch (error) {
        // Continue to next selector
      }
    }
    return null;
  }

  private async extractUrl(container: Locator, selectors: string[]): Promise<string | null> {
    for (const selector of selectors) {
      try {
        const element = container.locator(selector).first();
        const href = await element.getAttribute('href', { timeout: 1000 });
        if (href) {
          // Clean up Google redirect URLs
          return this.cleanUrl(href);
        }
      } catch (error) {
        // Continue to next selector
      }
    }
    return null;
  }

  private cleanUrl(url: string): string {
    // Remove Google redirect parameters
    if (url.startsWith('/url?')) {
      const params = new URLSearchParams(url.substring(5));
      const actualUrl = params.get('q') || params.get('url');
      if (actualUrl) {
        return actualUrl;
      }
    }
    
    if (url.startsWith('http')) {
      return url;
    }
    
    return url;
  }

  private async isAdvertisement(container: Locator): Promise<boolean> {
    for (const selector of this.selectors.adIndicator) {
      try {
        const adElement = container.locator(selector).first();
        const isVisible = await adElement.isVisible({ timeout: 500 });
        if (isVisible) {
          return true;
        }
      } catch (error) {
        // Continue checking
      }
    }

    // Check for ad text indicators
    try {
      const text = await container.textContent();
      if (text?.toLowerCase().includes('ad') || text?.toLowerCase().includes('sponsored')) {
        return true;
      }
    } catch (error) {
      // Ignore
    }

    return false;
  }

  private determineResultType(container: Locator, isAd: boolean): SearchResult['type'] {
    if (isAd) {
      return 'ad';
    }

    // Could add logic to detect featured snippets, people also ask, etc.
    return 'organic';
  }

  private async extractMetadata(container: Locator, url: string): Promise<SearchResult['metadata']> {
    try {
      const domain = new URL(url).hostname;
      
      // Extract breadcrumbs if available
      const breadcrumbs = await this.extractBreadcrumbs(container);
      
      // Extract sitelinks if available
      const sitelinks = await this.extractSitelinks(container);

      return {
        domain,
        breadcrumbs: breadcrumbs.length > 0 ? breadcrumbs : undefined,
        sitelinks: sitelinks.length > 0 ? sitelinks : undefined
      };
    } catch (error) {
      return {
        domain: 'unknown',
        breadcrumbs: undefined,
        sitelinks: undefined
      };
    }
  }

  private async extractBreadcrumbs(container: Locator): Promise<string[]> {
    const breadcrumbSelectors = ['.tjvcx', '.eIIj3e', '.AP7Wnd'];
    
    for (const selector of breadcrumbSelectors) {
      try {
        const elements = container.locator(selector);
        const count = await elements.count();
        const breadcrumbs: string[] = [];
        
        for (let i = 0; i < count; i++) {
          const text = await elements.nth(i).textContent();
          if (text && text.trim()) {
            breadcrumbs.push(text.trim());
          }
        }
        
        if (breadcrumbs.length > 0) {
          return breadcrumbs;
        }
      } catch (error) {
        // Continue to next selector
      }
    }
    
    return [];
  }

  private async extractSitelinks(container: Locator): Promise<string[]> {
    const sitelinkSelectors = ['.sVXRqc', '.oewGkc'];
    
    for (const selector of sitelinkSelectors) {
      try {
        const elements = container.locator(selector);
        const count = await elements.count();
        const sitelinks: string[] = [];
        
        for (let i = 0; i < count; i++) {
          const text = await elements.nth(i).textContent();
          if (text && text.trim()) {
            sitelinks.push(text.trim());
          }
        }
        
        if (sitelinks.length > 0) {
          return sitelinks;
        }
      } catch (error) {
        // Continue to next selector
      }
    }
    
    return [];
  }

  private async getElements(page: Page, selectors: string[]): Promise<Locator[]> {
    for (const selector of selectors) {
      try {
        const elements = page.locator(selector);
        const count = await elements.count();
        if (count > 0) {
          const locators: Locator[] = [];
          for (let i = 0; i < count; i++) {
            locators.push(elements.nth(i));
          }
          return locators;
        }
      } catch (error) {
        // Continue to next selector
      }
    }
    return [];
  }

  private async hasNextPage(page: Page): Promise<boolean> {
    for (const selector of this.selectors.nextPageButton) {
      try {
        const nextButton = page.locator(selector).first();
        const isVisible = await nextButton.isVisible({ timeout: 1000 });
        if (isVisible) {
          return true;
        }
      } catch (error) {
        // Continue checking
      }
    }
    return false;
  }

  private async getTotalResults(page: Page): Promise<number | undefined> {
    const resultStatsSelectors = ['#result-stats', '.LHJvCe'];
    
    for (const selector of resultStatsSelectors) {
      try {
        const element = page.locator(selector).first();
        const text = await element.textContent({ timeout: 1000 });
        if (text) {
          const match = text.match(/[\d,]+/);
          if (match) {
            return Number.parseInt(match[0].replace(/,/g, ''), 10);
          }
        }
      } catch (error) {
        // Continue to next selector
      }
    }
    
    return undefined;
  }

  private async getRelatedQueries(page: Page): Promise<string[]> {
    const relatedSelectors = ['.k8XOCe', '.BNeawe.s3v9rd.AP7Wnd'];
    
    for (const selector of relatedSelectors) {
      try {
        const elements = page.locator(selector);
        const count = await elements.count();
        const queries: string[] = [];
        
        for (let i = 0; i < count; i++) {
          const text = await elements.nth(i).textContent();
          if (text && text.trim()) {
            queries.push(text.trim());
          }
        }
        
        if (queries.length > 0) {
          return queries;
        }
      } catch (error) {
        // Continue to next selector
      }
    }
    
    return [];
  }

  private getCurrentPage(page: Page): number | undefined {
    try {
      const url = page.url();
      const urlParams = new URLSearchParams(new URL(url).search);
      const start = urlParams.get('start');
      
      if (start) {
        return Math.floor(Number.parseInt(start, 10) / 10) + 1;
      }
      
      return 1;
    } catch (error) {
      return undefined;
    }
  }

  /**
   * Count total number of Google Ads advertisers on the page
   */
  private async countAdsOnPage(page: Page): Promise<number> {
    try {
      // Extended ad selectors for comprehensive detection
      const adSelectors = [
        // Google Ads selectors
        '[data-text-ad]',
        '.ads-ad',
        '.ad_cclk',
        '.commercial-unit-desktop-top',
        '.pla-unit',
        '.shopping-unit',
        '.cu-container',
        '.ads-fr',
        '.ads-visurl',
        '.uEierd',
        '.mnr-c',
        '.pla-unit-container',
        // Shopping ads
        '.pla-unit-title',
        '.pla-unit-single-item-container',
        // Video ads
        '.videobox',
        // Text ads
        '[role="main"] .g:has([data-text-ad])',
        // Sponsored content indicators
        '.ad-tag',
        '.sponsored-tag'
      ];

      let totalAds = 0;

      for (const selector of adSelectors) {
        try {
          const elements = page.locator(selector);
          const count = await elements.count();
          totalAds += count;
        } catch (error) {
          // Continue with next selector
        }
      }

      // Additional check by looking for ad-related text content
      try {
        const adTextElements = await page.locator('*').evaluateAll((elements) => {
          return elements.filter(el => {
            const text = el.textContent?.toLowerCase() || '';
            return (text.includes('sponsored') || text.includes('ad') || text.includes('advertisement')) &&
                   !text.includes('address') && !text.includes('added') && !text.includes('advanced');
          }).length;
        });
        
        // Only add if not already counted by selectors
        if (totalAds === 0 && adTextElements > 0) {
          totalAds = adTextElements;
        }
      } catch (error) {
        logger.debug('Failed to count ads by text content:', error);
      }

      logger.info(`Found ${totalAds} ads on the page`);
      return totalAds;

    } catch (error) {
      logger.error('Failed to count ads on page:', error);
      return 0;
    }
  }

  /**
   * Count total number of links on the page
   */
  private async countLinksOnPage(page: Page): Promise<number> {
    try {
      const linkCount = await page.locator('a[href]').count();
      logger.info(`Found ${linkCount} total links on the page`);
      return linkCount;
    } catch (error) {
      logger.error('Failed to count links on page:', error);
      return 0;
    }
  }

  /**
   * Get HTML cache of the page
   */
  private async getPageHtmlCache(page: Page): Promise<string> {
    try {
      const htmlContent = await page.content();
      logger.info(`Cached HTML content (${htmlContent.length} characters)`);
      return htmlContent;
    } catch (error) {
      logger.error('Failed to get page HTML cache:', error);
      return '';
    }
  }
}