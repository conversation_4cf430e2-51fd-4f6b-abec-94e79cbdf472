{"name": "@search-keywords-scraper/scraper", "version": "0.1.0", "description": "Advanced search engine scraping with stealth configuration and anti-detection", "private": true, "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"build": "tsc", "dev": "tsc --watch", "lint": "biome lint src/", "type-check": "tsc --noEmit", "format": "biome format src/ --write"}, "dependencies": {"patchright": "^1.52.5", "cheerio": "^1.1.2", "axios": "^1.11.0", "winston": "^3.17.0", "zod": "^3.22.4", "uuid": "^11.1.0"}, "devDependencies": {"@types/node": "^22.10.7", "@types/uuid": "^10.0.0", "typescript": "^5.7.3"}, "exports": {".": {"import": "./dist/index.js", "require": "./dist/index.js", "types": "./dist/index.d.ts"}}, "license": "MIT"}